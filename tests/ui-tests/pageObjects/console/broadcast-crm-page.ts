import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const BroadcastPageUrl = '/class-management/broadcast-crm'

export class BroadcastPage extends ICPage {


    readonly broadcastTitle: Locator;
    readonly broadcastFilter: Locator;
    readonly createBroadcastButton: Locator;
    readonly senderDetailsHeader: Locator;
    readonly createBroadcastHearder: Locator;
    readonly sendAsText: Locator;
    readonly toStudentText: Locator;
    readonly broadcastDetailsText: Locator;
    readonly addAttachment: Locator;
    readonly attachmentInput: Locator;
    readonly titleOfBroadcast: Locator;
    readonly inputForTitle: Locator;
    readonly broadcastCategory: Locator;
    readonly selectBroadcast: Locator;
    readonly broadcastAsGeneral: Locator;
    readonly priorityTitle: Locator;
    readonly selectPriority: Locator;
    readonly priorityAsMedium: Locator;
    readonly expiresOnTitle: Locator;
    readonly expiresOnInput: Locator;
    readonly descriptionTitle: Locator;
    readonly descriptionPlaceholder: Locator;
    readonly sendBroadcastTo: Locator;
    readonly centerTitle: Locator;
    readonly selectCenter: Locator;
    readonly searchInput: Locator;
    readonly automationCenter: Locator;
    readonly courseTitle: Locator;
    readonly selectCourse: Locator;
    readonly automationCourse: Locator;
    readonly campusTitle: Locator;
    readonly selectCampus: Locator;
    readonly automationCampus: Locator;
    readonly streamTitle: Locator;
    readonly selectStream: Locator;
    readonly jeeAdvancedStream: Locator;
    readonly classTitle: Locator;
    readonly selectClass: Locator;
    readonly class11Option: Locator;
    readonly phaseTitle: Locator;
    readonly selectPhase: Locator;
    readonly selectAutomationPhase: Locator;
    readonly phase1Option: Locator;
    readonly batchTitle: Locator;
    readonly selectBatch: Locator;
    readonly batchName: (batchName: string) => Locator;
    readonly createdBroadcastName: (createdBroadcastName: string) => Locator;
    readonly saveAsDraftOption: Locator;
    readonly liveButton: Locator;
    readonly broadcastLiveSuccessfullyTost: Locator;
    readonly broadcastPublishedSuccessfullyTost: Locator;
    readonly updateBroadcastTitle: Locator;
    readonly deleteCreatedCrmButtonDots: (createdBroadcastId: string) => Locator;
    readonly deleteButton: Locator;
    readonly deleteBroadcastPopupTitle: Locator;
    readonly areyousureText: Locator;
    readonly continueButton: Locator;
    readonly cancelButton: Locator;
    readonly deletedCrmTostText: Locator;
    readonly teacherOption: Locator;
    readonly teacherRadioButton: Locator;
    readonly selectTeacher: Locator;
    readonly searchteacherInput: Locator;
    readonly teacherName: (teacherName: string) => Locator;
    readonly searchButton: Locator;
    readonly scheduledDateTitle: Locator;
    readonly publishDateInput: Locator;
    readonly publishButton: Locator;
    readonly academicSessionsTitle: Locator;
    readonly selectAcademicSession: Locator;
    readonly academicSessionValue: Locator;
    readonly sendBroadcastButton: Locator;


    constructor(page: Page, isMobile: boolean) {
        super(page, BroadcastPageUrl, isMobile);
        this.broadcastTitle = page.getByText('Broadcast CRM', { exact: true });
        this.broadcastFilter = page.getByText('Filter', { exact: true });
        this.createBroadcastButton = page.getByTestId('sort-search-primary-button').getByText('Create Broadcast');
        this.senderDetailsHeader = page.getByText('Sender Details & Sendee Type');
        this.createBroadcastHearder = page.getByRole('heading', { name: 'Create Broadcast' });
        this.sendAsText = page.getByText('Send as:');
        this.toStudentText = page.getByText('To: Student');
        this.broadcastDetailsText = page.getByText('Broadcast Details');
        this.addAttachment = page.getByRole('button', { name: 'Add Attachment Max 20 MB' });
        this.attachmentInput = page.locator("input[type='file']");
        this.titleOfBroadcast = page.getByText('Title*');
        this.inputForTitle = page.getByTestId('textInput');
        this.broadcastCategory = page.getByText('Broadcast Category *');
        this.selectBroadcast = page.getByText('SelectBroadcast Category');
        this.broadcastAsGeneral = page.getByTestId('dropdown-option-General');
        this.priorityTitle = page.getByText('Priority *');
        this.selectPriority = page.getByText('Select Priority');
        this.priorityAsMedium = page.getByTestId('dropdown-option-Medium');
        this.expiresOnTitle = page.getByText('Expires On *');
        this.expiresOnInput = page.getByPlaceholder('Expires On');
        this.descriptionTitle = page.getByText('Description*');
        this.descriptionPlaceholder = page.getByLabel('Description');
        this.sendBroadcastTo = page.getByText('Send broadcast to');
        this.centerTitle = page.getByText('Center *');
        this.selectCenter = page.getByText('Select Center');
        this.automationCenter = page.getByTestId('dropdown-option-fa_npghQRtILotkeoZyLmfAO');
        this.searchInput = page.getByPlaceholder('Search...');
        this.courseTitle = page.getByText('Course', { exact: true });
        this.selectCourse = page.getByText('Select Course');
        this.automationCourse = page.getByTestId('dropdown-option-cr_WhO1B61BXTumqJvtEWz9F');
        this.campusTitle = page.getByText('Campus', { exact: true });
        this.selectCampus = page.getByText('Select Campus');
        this.automationCampus = page.getByTestId('dropdown-option-fa_npghQRtILotkeoZyLmfAO');
        this.streamTitle = page.getByText('Stream', { exact: true });
        this.selectStream = page.getByText('Select Stream');
        this.jeeAdvancedStream = page.getByTestId('dropdown-option-STREAM_JEE_MAIN_ADVANCED');
        this.classTitle = page.getByText('Class', { exact: true });
        this.selectClass = page.getByText('Select Class');
        this.class11Option = page.getByTestId('dropdown-option-CLASS_11');
        this.phaseTitle = page.getByText('Phase', { exact: true });
        this.selectPhase = page.getByText('Select Phase');
        this.selectAutomationPhase = page.getByText('Automation Testing');
        this.phase1Option = page.getByText('1', { exact: true });
        this.batchTitle = page.getByText('Batch', { exact: true });
        this.selectBatch = page.getByText('Select Batch');
        this.batchName = (batchName: string) => page.getByText(`${batchName}`, { exact: true });
        this.saveAsDraftOption = page.getByTestId('save-as-draft');
        this.liveButton = page.getByTestId('cta-button').getByText('Send Broadcast');
        this.broadcastLiveSuccessfullyTost = page.getByText('Broadcast live successfully');
        this.broadcastPublishedSuccessfullyTost = page.getByText('Broadcast Scheduled successfully');
        this.createdBroadcastName = (createdBroadcastName: string) => page.getByText(`${createdBroadcastName}`, { exact: true });
        this.updateBroadcastTitle = page.getByText('Update Broadcast');
        this.deleteCreatedCrmButtonDots = (createdBroadcastId: string) => page.getByTestId(`facility-action-cell-${createdBroadcastId}`).getByTestId('shownImage');
        this.deleteButton = page.getByTestId('delete-notice');
        this.areyousureText = page.getByText('Are you sure you want to');
        this.deleteBroadcastPopupTitle = page.getByText('Delete Broadcast');
        this.continueButton = page.getByTestId('continue-button');
        this.cancelButton = page.getByTestId('cta-button').getByText('Cancel');
        this.deletedCrmTostText = page.getByText('Broadcast deleted successfully! Page will refresh');
        this.teacherOption = page.getByText('Teacher');
        this.teacherRadioButton = page.getByLabel('Teacher');
        this.selectTeacher = page.getByText('Select Teacher');
        this.searchteacherInput = page.getByTestId('search-input');
        this.teacherName = (teacherName: string) => page.getByText(`${teacherName}`);
        this.searchButton = page.getByTestId('text-inside-input');
        this.scheduledDateTitle = page.getByText('Scheduled Date');
        this.publishDateInput = page.getByPlaceholder('Required for publish');
        this.publishButton = page.getByTestId('cta-button').getByText('Publish');
        this.academicSessionsTitle = page.getByText('Academic Sessions *');
        this.selectAcademicSession = page.getByText('Select Academic Session');
        this.academicSessionValue = page.getByTestId('dropdown-option-04_2024__03_2025');
        this.sendBroadcastButton = page.getByTestId('modal').getByRole('button', { name: 'Send Broadcast' });
    }
    async validateBroadcastPage() {
        await expect(this.broadcastTitle, 'Verify broadcast title is visible').toBeVisible();
        await expect(this.broadcastFilter, 'Verify broadcast filter option is visible').toBeVisible();
        await expect(this.createBroadcastButton, 'Verify create broadcast button is visible').toBeVisible();
    }

    async validateAndFillScheduledDate(currentDate) {
        await expect(this.scheduledDateTitle, 'Verify scheduled Date Title is visible').toBeVisible();
        await expect(this.publishDateInput, 'Verify publish Date Input is visible').toBeVisible();
        await this.publishDateInput.fill(currentDate)
    }

    async validateCreateBroadcastPage() {
        await expect(this.createBroadcastHearder, 'Verify create broadcast header is visible').toBeVisible();
        await expect(this.senderDetailsHeader, 'Verify sender details header is visible').toBeVisible();
        await expect(this.sendAsText, 'Verify send as text is visible').toBeVisible();
        await expect(this.toStudentText, 'Verify send to student text is visible').toBeVisible();
        await expect(this.broadcastDetailsText, 'Verify broadcast details text is visible').toBeVisible();
    }
    async selectTeacherForBroadcast(findTeacher, teaherName) {
        await expect(this.teacherOption, 'Verify teacher option is visible').toBeVisible();
        await expect(this.teacherRadioButton, 'Verify teacher radio button is visible').toBeVisible();
        await this.teacherRadioButton.click();
        await expect(this.selectTeacher, 'Verify select teacher is visible').toBeVisible();
        await expect(this.searchteacherInput, 'Verify search teacher input is visible').toBeVisible();
        await this.searchteacherInput.fill(findTeacher);
        await expect(this.searchButton, 'Verify search button is visible').toBeVisible();
        await this.searchButton.click();
        await expect(this.teacherName(teaherName), 'Verify teacher name is visible').toBeVisible();
    }
    async fillDetailsTocreateBroadcast(titleNameOfBroadcast, expiryDate, crmDescription, centerName, campusName, streamName, nameOfBatch, broadcastAttachment) {
        /* Fill attachment */
        await expect(this.addAttachment, 'Verify add attachment is visible').toBeVisible();
        await this.attachmentInput.setInputFiles(broadcastAttachment);
        /* filling broadcast details */
        await expect(this.titleOfBroadcast, 'Verify title of broadcast is visible').toBeVisible();
        await expect(this.inputForTitle, 'Verify input of title is visible').toBeVisible();
        await this.inputForTitle.fill(titleNameOfBroadcast);
        await expect(this.broadcastCategory, 'Verify broadcast category title is visible').toBeVisible();
        await expect(this.selectBroadcast, 'Verify select broadcast category is visible').toBeVisible();
        await this.selectBroadcast.click();
        await expect(this.broadcastAsGeneral, 'Verify select broadcast as General is visible').toBeVisible();
        await this.broadcastAsGeneral.click();
        await expect(this.priorityTitle, 'Verify priority title is visible').toBeVisible();
        await expect(this.selectPriority, 'Verify select priority is visible').toBeVisible();
        await this.selectPriority.click();
        await expect(this.priorityAsMedium, 'Verify priority medium option is visible').toBeVisible();
        await this.priorityAsMedium.click();
        await expect(this.expiresOnTitle, 'Verify expires on title is visible').toBeVisible();
        await this.expiresOnInput.fill(expiryDate);
        await expect(this.descriptionTitle, 'Verify description title is visible').toBeVisible();
        await expect(this.descriptionPlaceholder, 'Verify description input title is visible').toBeVisible();
        await this.descriptionPlaceholder.fill(crmDescription);
        /* send broadcast to (batch details) */
        await expect(this.centerTitle, 'Verify center title is visible').toBeVisible();
        await expect(this.selectCenter, 'Verify select center is visible').toBeVisible();
        await this.selectCenter.click();
        await expect(this.searchInput, 'Verify center search input is visible').toBeVisible();
        await this.searchInput.fill(centerName);
        await expect(this.automationCenter, 'Verify automation center option is visible').toBeVisible();
        await this.automationCenter.click();
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(1000); // wait required to load the data

        await expect(this.campusTitle, 'Verify campus title is visible').toBeVisible();
        await expect(this.selectCampus, 'Verify select campus is visible').toBeVisible();
        await this.selectCampus.click();
        await expect(this.searchInput, 'Verify campus search input is visible').toBeVisible();
        await this.searchInput.fill(campusName);
        await expect(this.automationCampus, 'Verify automation campus option is visible').toBeVisible();
        await this.automationCampus.click();
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(1000); // wait required to load the data

        await expect(this.streamTitle, 'Verify stream title is visible').toBeVisible();
        await expect(this.selectStream, 'Verify select stream is visible').toBeVisible();
        await this.selectStream.click();
        await expect(this.searchInput, 'Verify stream search input is visible').toBeVisible();
        await this.searchInput.fill(streamName);
        await expect(this.jeeAdvancedStream, 'Verify jee advanced stream option is visible').toBeVisible();
        await this.jeeAdvancedStream.click();
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(1000); // wait required to load the data

        await expect(this.academicSessionsTitle, 'Verify academic session title is visible').toBeVisible();
        await expect(this.selectAcademicSession, 'Verify select academic session is visible').toBeVisible();
        await this.selectAcademicSession.click();
        await expect(this.academicSessionValue, 'Verify academic session value is visible').toBeVisible();
        await this.academicSessionValue.click();
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(15000); // wait required to load the data

        await expect(this.classTitle, 'Verify class title is visible').toBeVisible();
        await expect(this.selectClass, 'Verify select class is visible').toBeVisible();
        await this.selectClass.click();
        await expect(this.class11Option, 'Verify class 11 option is visible').toBeVisible();
        await this.class11Option.click();
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(1000); // wait required to load the data

        await expect(this.courseTitle, 'Verify course title is visible').toBeVisible();
        await expect(this.selectCourse, 'Verify select course is visible').toBeVisible();
        await this.selectCourse.click();
        /*
        * Added wait for response network event
        */
        await this.page.waitForTimeout(2000);
        await expect(this.automationCourse, 'Verify automation course option is visible').toBeVisible();
        const responsePromise = this.page.waitForResponse('https://bff.allen-stage.in/internal-bff/api/v1/resource/batches/filter ', { timeout: 10000 });
        await this.automationCourse.click();
        await responsePromise;


        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(1000); // wait required to load the data

        await expect(this.phaseTitle, 'Verify phase title is visible').toBeVisible();
        await expect(this.selectPhase, 'Verify select phase is visible').toBeVisible();
        await this.selectPhase.click();
        await expect(this.selectAutomationPhase, 'Verify automation phase is visible').toBeVisible();
        await this.selectAutomationPhase.click();
        await expect(this.phase1Option, 'Verify phase option is visible').toBeVisible();
        await this.phase1Option.click();
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForLoadState('domcontentloaded');
        await this.page.waitForTimeout(1000);// wait required to load the data

        await expect(this.batchTitle, 'Verify batch title is visible').toBeVisible();
        await this.batchTitle.click();
        await this.page.waitForLoadState('domcontentloaded');
        await this.page.waitForLoadState('networkidle');
        await expect(this.selectBatch, 'Verify select batch is visible').toBeVisible();
        await this.selectBatch.click();
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForLoadState('domcontentloaded');
        await expect(this.selectAutomationPhase, 'Verify automation batch is visible').toBeVisible();
        await this.selectAutomationPhase.click();
        await expect(this.searchInput, 'Verify batch search input is visible').toBeVisible();
        await this.searchInput.fill(nameOfBatch);
        await expect(this.batchName(nameOfBatch), 'Verify batch option is visible').toBeVisible();
        await this.batchName(nameOfBatch).click();
        await expect(this.saveAsDraftOption, 'Verify save as draft button is visible').toBeVisible();
    }

    async validateandDeleteCreatedCRM(titleNameOfBroadcast, createdBroadcastId) {
        await slowExpect(this.createdBroadcastName(titleNameOfBroadcast), 'Verifying created broadcast title text should be visible').toBeVisible();
        await expect(this.deleteCreatedCrmButtonDots(createdBroadcastId), 'Verify Delete Action button is visible').toBeVisible();
        await this.deleteCreatedCrmButtonDots(createdBroadcastId).click();
        await expect(this.deleteButton, 'Verify delete button is visible').toBeVisible();
        await this.deleteButton.click();
        await expect(this.deleteBroadcastPopupTitle, 'Verify delete broadcast popup title is visible').toBeVisible();
        await expect(this.areyousureText, 'Verify are you sure to delete text is visible').toBeVisible();
        await expect(this.continueButton, 'Verify continue button is visible').toBeVisible();
        await expect(this.cancelButton, 'Verify cancel button is visible').toBeVisible();
        await this.continueButton.click();
        await slowExpect(this.deletedCrmTostText, 'Verify broadcast deleted toast text is visible').toBeVisible();
        await slowExpect(this.createdBroadcastName(titleNameOfBroadcast), 'Verifying deleted broadcast should not be visible').toBeHidden();
    }
}
