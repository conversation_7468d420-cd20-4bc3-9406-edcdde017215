import { test, expect, customExpect, slowExpect } from '../fixtures/ui-fixtures';
import { StringUtils, PlaywrightUtils } from '../../commons/common-functions';
import { format } from 'path';


test.describe('CRM UI Tests', {
    tag: ['@crm', '@ui']
}, () => {
    const env = process.env.PROD === "1" ? "prod" : "stage";
    if (env == 'stage') {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowDate = tomorrow.getTime();
        // Verify broadcast creation as admin/teacher with live/publish options, validate from student side
        test('Verify Admin/Teacher can create broadcast with live/publish options and validate student side', async ({ adminPage, studentPage, testData }) => {
            test.setTimeout(2400000);
            const broadcasts = [
                {
                    title: "automation_" + StringUtils.generateRandomString(3),
                    type: "admin",
                    isLive: true
                },
                {
                    title: "automation_" + StringUtils.generateRandomString(3),
                    type: "teacher",
                    isLive: false
                },
                {
                    title: "automation_" + StringUtils.generateRandomString(3),
                    type: "admin",
                    isLive: false
                },
                {
                    title: "automation_" + StringUtils.generateRandomString(3),
                    type: "teacher",
                    isLive: true
                }
            ];

            const commonData = {
                expiryDate: StringUtils.getTomorrowDate(),
                crmDescription: "https://www.google.com/",
                centerName: "automation",
                campusName: "automation",
                streamName: "jee advanced",
                nameOfBatch: testData.batch.name,
                boradcastCategory: "General",
                teacherEmail: "<EMAIL>",
                teacherName: "Allen Automation Report",
                broadcastAttachment: "test-data/doubt-Image.png"
            };

            const successfulBroadcasts: typeof broadcasts = []; // Array to track successful broadcasts
            const creationErrors: string[] = []; // Array to track creation errors
            const errors: string[] = []; // Declare the errors array
            const deletionErrors: string[] = []; // Declare the deletion errors array

            await test.step('Navigate to broadcast page', async () => {
                await adminPage.goto(adminPage.pos.broadcastPage.url);
                await adminPage.waitForLoadState('networkidle');
            });

            // Create the first broadcast using UI
            await test.step(`Create ${broadcasts[0].type} broadcast with live option`, async () => {
                await slowExpect(adminPage).toHaveURL(/.*broadcast-crm/);
                await adminPage.waitForLoadState('networkidle');
                await adminPage.pos.broadcastPage.validateBroadcastPage();
                await adminPage.pos.broadcastPage.createBroadcastButton.click();
                await adminPage.waitForLoadState('networkidle');

                await adminPage.pos.broadcastPage.validateCreateBroadcastPage();
                await adminPage.pos.broadcastPage.fillDetailsTocreateBroadcast(
                    broadcasts[0].title,
                    commonData.expiryDate,
                    commonData.crmDescription,
                    commonData.centerName,
                    commonData.campusName,
                    commonData.streamName,
                    commonData.nameOfBatch,
                    commonData.broadcastAttachment
                );
                await expect(adminPage.pos.broadcastPage.liveButton, 'Verify live button is visible').toBeVisible();
                await adminPage.pos.broadcastPage.liveButton.click();
                await adminPage.pos.broadcastPage.sendBroadcastButton.click();
                await slowExpect(adminPage.pos.broadcastPage.broadcastLiveSuccessfullyTost, 'Verify broadcast live successfully toast is visible').toBeVisible();
                successfulBroadcasts.push(broadcasts[0]);
            });

            // Create the remaining broadcasts using the API
            for (let i = 1; i < broadcasts.length; i++) {
                const tomorrowDateEpoch = StringUtils.getTomorrowDate(true);
                const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
                const currentDate = StringUtils.getCurrentTimeWithOffset("YYYY-MM-DD", 0, false, timezone);
                const currentTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 30, false, timezone);
                const broadcast = broadcasts[i];
                await test.step(`Create ${broadcast.type} broadcast with ${broadcast.isLive ? 'live' : 'publish'} option using API`, async () => {
                    try {
                        await adminPage.waitForTimeout(2000);
                        const sender = broadcast.type === "admin" ? "NOTICE_SENDER_ADMIN" : "NOTICE_SENDER_TEACHER";
                        await adminPage.apis.resourceManagement.sendNotice(
                            broadcast.title,
                            commonData.crmDescription,
                            commonData.boradcastCategory,
                            parseInt(tomorrowDateEpoch),
                            process.env.CENTER_ID ? process.env.CENTER_ID : "",
                            testData.batch.course,
                            testData.batch.id,
                            testData.batch.phase,
                            process.env.CENTER_ID ? process.env.CENTER_ID : "",
                            "STREAM_JEE_MAIN_ADVANCED",
                            "CLASS_11",
                            broadcast.type === "admin" ? process.env.ADMIN_ID ? process.env.ADMIN_ID : "" : process.env.ADMIN_ID ? process.env.ADMIN_ID : "",
                            sender,
                            broadcast.isLive
                                ? undefined // Do not pass schedule when live
                                : { expression: currentDate + "T" + currentTime + ":00" } // Pass schedule for non-live broadcasts
                        );
                        successfulBroadcasts.push(broadcast);
                    } catch (error) {
                        creationErrors.push(`Failed to create ${broadcast.type} broadcast: ${error.message}`);
                    }
                });
            }

            // Wait for scheduled broadcasts to be published
            await studentPage.waitForTimeout(60000);

            // Validate admin/teacher with live/publish broadcasts from student side
            await test.step('Validate admin/teacher created broadcasts with live/publish options is visible from student side', async () => {
                await test.step('Navigate to noticeboard by login as student', async () => {
                    await studentPage.login();
                    await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text visibility').toBeVisible();
                    await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();
                    await studentPage.pos.homePage.userNameMenu.hover();
                    await expect(studentPage.pos.homePage.noticeboardMenu, "Verify noticeboard menu is visible").toBeVisible();
                    await studentPage.pos.homePage.noticeboardMenu.click();
                    await expect(studentPage).toHaveURL(/.*view=noticeboard/);

                    // Reload and wait strategy: reload page and wait 30 seconds, repeat up to 10 times until notices are visible
                    let reloadAttempts = 0;
                    const maxReloadAttempts = 10;
                    let noticesVisible = false;

                    while (reloadAttempts < maxReloadAttempts && !noticesVisible) {
                        console.log(`Reload attempt ${reloadAttempts + 1}/${maxReloadAttempts}`);

                        // Reload the page
                        await studentPage.reload();
                        await studentPage.waitForLoadState('networkidle');

                        // Wait for 10 seconds
                        await studentPage.waitForTimeout(30000);

                        // Check if notices are visible by trying to find any broadcast
                        try {
                            // Check if any of the successful broadcasts are visible
                            for (const broadcast of successfulBroadcasts) {
                                try {
                                    await expect(studentPage.pos.profilePage.broadcastName(broadcast.title)).toBeVisible({ timeout: 5000 });
                                    console.log(`Notice found: ${broadcast.title}`);
                                    noticesVisible = true;
                                    break;
                                } catch (error) {
                                    // Continue checking other broadcasts
                                }
                            }

                            // If no specific broadcasts found, check for loading notification
                            if (!noticesVisible) {
                                const isLoadingVisible = await studentPage.pos.profilePage.loadingNotification.isVisible();
                                if (!isLoadingVisible) {
                                    console.log('Loading notification not visible, assuming notices are loaded');
                                    noticesVisible = true;
                                }
                            }
                        } catch (error) {
                            console.log(`Error checking notices visibility: ${error.message}`);
                        }

                        reloadAttempts++;
                    }

                    if (!noticesVisible) {
                        console.log(`Warning: Notices may not be visible after ${maxReloadAttempts} reload attempts`);
                    } else {
                        console.log(`Notices are visible after ${reloadAttempts} reload attempts`);
                    }
                });

                // Validate each broadcast only once
                for (const broadcast of successfulBroadcasts) {
                    try {
                        await test.step(`Validate ${broadcast.type} broadcast with ${broadcast.title}`, async () => {
                            await studentPage.pos.profilePage.verifyBroadcastInNoticeboard(broadcast.title, commonData.boradcastCategory);
                            if (broadcast.type === 'admin') {
                                await expect(studentPage.pos.profilePage.fromAdminText, "Verify broadcast from admin is visible").toBeVisible();
                            } else {
                                await expect(studentPage.pos.profilePage.broadcastName(commonData.teacherName), "Verify broadcast from teacher is visible").toBeVisible();
                            }
                            await slowExpect(studentPage.pos.profilePage.backToNoticeBoardButton, "Verify goback button is visible").toBeVisible();
                            await studentPage.goBack();
                            await expect(studentPage).toHaveURL(/.*view=noticeboard/);
                        });
                    } catch (error) {
                        errors.push(`Failed to validate ${broadcast.type} broadcast with "${broadcast.title}": ${error.message}`);
                    }
                }

                // Report all validation errors at once
                if (errors.length > 0) {
                    throw new Error('Broadcast validation errors:\n' + errors.join('\n'));
                }
            });

            // Add deletion for live broadcasts after student validation
            await test.step('Delete live broadcasts', async () => {
                for (const broadcast of successfulBroadcasts) {
                    if (broadcast.isLive) {
                        try {
                            await slowExpect(adminPage.pos.broadcastPage.createdBroadcastName(broadcast.title), 'Verifying created broadcast title text should be visible').toBeVisible();
                            await adminPage.pos.broadcastPage.createdBroadcastName(broadcast.title).click();
                            await slowExpect(adminPage.pos.broadcastPage.updateBroadcastTitle, 'Verifying update created broadcast title should be visible').toBeVisible();
                            expect(adminPage.url()).toBeTruthy();
                            const full_url = adminPage.url();
                            const crm_id = full_url.split('/').pop();
                            await adminPage.goBack();
                            await adminPage.pos.broadcastPage.validateandDeleteCreatedCRM(broadcast.title, crm_id);
                        } catch (error) {
                            deletionErrors.push(`Failed to delete broadcast "${broadcast.title}": ${error.message}`);
                        }
                    }
                }

                // Report all deletion errors after completing deletion attempts
                if (deletionErrors.length > 0) {
                    throw new Error('Broadcast deletion errors:\n' + deletionErrors.join('\n'));
                }
            });

            // Collect all errors at the end of test
            const allErrors: string[] = [];
            if (errors.length > 0) {
                allErrors.push('Broadcast Validation Errors:\n' + errors.join('\n'));
            }
            if (deletionErrors.length > 0) {
                allErrors.push('Broadcast Deletion Errors:\n' + deletionErrors.join('\n'));
            }

            // Throw all collected errors at the end
            if (allErrors.length > 0) {
                throw new Error('Test Failed with following errors:\n\n' + allErrors.join('\n\n'));
            }
        });
    }
});